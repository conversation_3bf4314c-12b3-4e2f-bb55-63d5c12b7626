import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:text_generation_video/app/view/photo_restoration/photo_portrait_category_page.dart';

void main() {
  group('PhotoPortraitCategoryPage Tests', () {
    testWidgets('页面应该正确渲染', (WidgetTester tester) async {
      // 构建测试应用
      await tester.pumpWidget(
        ProviderScope(
          child: ScreenUtilInit(
            designSize: const Size(375, 812),
            child: MaterialApp(
              home: const PhotoPortraitCategoryPage(),
            ),
          ),
        ),
      );

      // 等待页面渲染完成
      await tester.pumpAndSettle();

      // 验证 AppBar 标题
      expect(find.text('写真分类'), findsOneWidget);

      // 验证分类标签存在
      expect(find.text('热门'), findsOneWidget);
      expect(find.text('双重曝光'), findsOneWidget);
      expect(find.text('网感大片'), findsOneWidget);
      expect(find.text('宠物写真'), findsOneWidget);
    });

    testWidgets('应该能够切换分类', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: ScreenUtilInit(
            designSize: const Size(375, 812),
            child: MaterialApp(
              home: const PhotoPortraitCategoryPage(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 点击"双重曝光"分类
      await tester.tap(find.text('双重曝光'));
      await tester.pumpAndSettle();

      // 验证分类切换成功（可以通过检查选中状态的样式来验证）
      // 这里我们主要验证没有抛出异常
      expect(find.text('双重曝光'), findsOneWidget);
    });

    testWidgets('Provider 应该正确管理状态', (WidgetTester tester) async {
      late WidgetRef ref;
      
      await tester.pumpWidget(
        ProviderScope(
          child: ScreenUtilInit(
            designSize: const Size(375, 812),
            child: MaterialApp(
              home: Consumer(
                builder: (context, widgetRef, child) {
                  ref = widgetRef;
                  return const PhotoPortraitCategoryPage();
                },
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态
      expect(ref.read(selectedCategoryIndexProvider), 0);

      // 验证分类数据
      final categories = ref.read(photoPortraitCategoryDataProvider);
      expect(categories.length, 4);
      expect(categories[0].caseName, '热门');
      expect(categories[1].caseName, '双重曝光');
      expect(categories[2].caseName, '网感大片');
      expect(categories[3].caseName, '宠物写真');
    });

    testWidgets('应该显示图片网格', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: ScreenUtilInit(
            designSize: const Size(375, 812),
            child: MaterialApp(
              home: const PhotoPortraitCategoryPage(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证 GridView 存在
      expect(find.byType(GridView), findsOneWidget);
    });
  });
}
