import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_portrait.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';

part 'photo_portrait_category_page.g.dart';

// 当前选中的分类索引 Provider
@riverpod
class SelectedCategoryIndex extends _$SelectedCategoryIndex {
  @override
  int build() {
    return 0;
  }

  void selectCategory(int index) {
    state = index;
  }
}

// 分类数据 Provider
@riverpod
class PhotoPortraitCategoryData extends _$PhotoPortraitCategoryData {
  @override
  List<PhotoPortraitCategory> build() {
    return _getMockCategoryData();
  }

  // 模拟数据 - 写真分类数据（与主页面保持一致）
  List<PhotoPortraitCategory> _getMockCategoryData() => [
        PhotoPortraitCategory()
          ..caseName = "热门"
          ..id = 1
          ..sort = 1
          ..state = 1
          ..details = [
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/300?random=1"
              ..caseTitle = "热门写真1"
              ..casePrompt = "时尚热门写真风格"
              ..caseId = 101
              ..caseWidth = 200
              ..caseHeight = 300,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/350?random=2"
              ..caseTitle = "热门写真2"
              ..casePrompt = "流行写真风格"
              ..caseId = 102
              ..caseWidth = 200
              ..caseHeight = 350,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/280?random=3"
              ..caseTitle = "热门写真3"
              ..casePrompt = "经典写真风格"
              ..caseId = 103
              ..caseWidth = 200
              ..caseHeight = 280,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/320?random=4"
              ..caseTitle = "热门写真4"
              ..casePrompt = "现代写真风格"
              ..caseId = 104
              ..caseWidth = 200
              ..caseHeight = 320,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/290?random=17"
              ..caseTitle = "热门写真5"
              ..casePrompt = "优雅写真风格"
              ..caseId = 105
              ..caseWidth = 200
              ..caseHeight = 290,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/310?random=18"
              ..caseTitle = "热门写真6"
              ..casePrompt = "清新写真风格"
              ..caseId = 106
              ..caseWidth = 200
              ..caseHeight = 310,
          ],
        PhotoPortraitCategory()
          ..caseName = "双重曝光"
          ..id = 2
          ..sort = 2
          ..state = 1
          ..details = [
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/290?random=5"
              ..caseTitle = "双重曝光1"
              ..casePrompt = "艺术双重曝光效果"
              ..caseId = 201
              ..caseWidth = 200
              ..caseHeight = 290,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/310?random=6"
              ..caseTitle = "双重曝光2"
              ..casePrompt = "创意双重曝光"
              ..caseId = 202
              ..caseWidth = 200
              ..caseHeight = 310,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/270?random=7"
              ..caseTitle = "双重曝光3"
              ..casePrompt = "梦幻双重曝光"
              ..caseId = 203
              ..caseWidth = 200
              ..caseHeight = 270,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/340?random=8"
              ..caseTitle = "双重曝光4"
              ..casePrompt = "专业双重曝光"
              ..caseId = 204
              ..caseWidth = 200
              ..caseHeight = 340,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/300?random=19"
              ..caseTitle = "双重曝光5"
              ..casePrompt = "唯美双重曝光"
              ..caseId = 205
              ..caseWidth = 200
              ..caseHeight = 300,
          ],
        PhotoPortraitCategory()
          ..caseName = "网感大片"
          ..id = 3
          ..sort = 3
          ..state = 1
          ..details = [
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/300?random=9"
              ..caseTitle = "网感大片1"
              ..casePrompt = "时尚网感风格"
              ..caseId = 301
              ..caseWidth = 200
              ..caseHeight = 300,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/280?random=10"
              ..caseTitle = "网感大片2"
              ..casePrompt = "潮流网感风格"
              ..caseId = 302
              ..caseWidth = 200
              ..caseHeight = 280,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/320?random=11"
              ..caseTitle = "网感大片3"
              ..casePrompt = "个性网感风格"
              ..caseId = 303
              ..caseWidth = 200
              ..caseHeight = 320,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/290?random=12"
              ..caseTitle = "网感大片4"
              ..casePrompt = "酷炫网感风格"
              ..caseId = 304
              ..caseWidth = 200
              ..caseHeight = 290,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/310?random=20"
              ..caseTitle = "网感大片5"
              ..casePrompt = "前卫网感风格"
              ..caseId = 305
              ..caseWidth = 200
              ..caseHeight = 310,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/270?random=21"
              ..caseTitle = "网感大片6"
              ..casePrompt = "炫酷网感风格"
              ..caseId = 306
              ..caseWidth = 200
              ..caseHeight = 270,
          ],
        PhotoPortraitCategory()
          ..caseName = "宠物写真"
          ..id = 4
          ..sort = 4
          ..state = 1
          ..details = [
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/310?random=13"
              ..caseTitle = "宠物写真1"
              ..casePrompt = "可爱宠物风格"
              ..caseId = 401
              ..caseWidth = 200
              ..caseHeight = 310,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/270?random=14"
              ..caseTitle = "宠物写真2"
              ..casePrompt = "萌宠写真风格"
              ..caseId = 402
              ..caseWidth = 200
              ..caseHeight = 270,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/330?random=15"
              ..caseTitle = "宠物写真3"
              ..casePrompt = "温馨宠物风格"
              ..caseId = 403
              ..caseWidth = 200
              ..caseHeight = 330,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/300?random=16"
              ..caseTitle = "宠物写真4"
              ..casePrompt = "活泼宠物风格"
              ..caseId = 404
              ..caseWidth = 200
              ..caseHeight = 300,
          ],
      ];
}

class PhotoPortraitCategoryPage extends ConsumerWidget {
  const PhotoPortraitCategoryPage({
    super.key,
    this.categoryId = 0,
  });

  /// 分类ID
  final int categoryId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final categories = ref.watch(photoPortraitCategoryDataProvider);
    final selectedIndex = ref.watch(selectedCategoryIndexProvider);

    return Scaffold(
      backgroundColor: const Color(0xFF18161A),
      appBar: _buildAppBar(context),
      body: Column(
        children: [
          _buildCategoryTabs(context, ref, categories, selectedIndex),
          Expanded(
            child: _buildCategoryContent(context, categories, selectedIndex),
          ),
        ],
      ),
    );
  }

  // 构建 AppBar
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.transparent,
      centerTitle: true,
      toolbarHeight: 44.h,
      title: const Text(
        "写真分类",
        style: TextStyle(
          fontSize: 16,
          color: Colors.white,
        ),
      ),
      leading: const Leading(
        color: Colors.white,
      ),
    );
  }

  // 构建分类标签导航栏
  Widget _buildCategoryTabs(BuildContext context, WidgetRef ref,
      List<PhotoPortraitCategory> categories, int selectedIndex) {
    return Container(
      height: 50.h,
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = index == selectedIndex;

          return GestureDetector(
            onTap: () {
              ref
                  .read(selectedCategoryIndexProvider.notifier)
                  .selectCategory(index);
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              margin: EdgeInsets.only(right: 20.w),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              decoration: BoxDecoration(
                color:
                    isSelected ? const Color(0xFF30E6B8) : Colors.transparent,
                borderRadius: BorderRadius.circular(20.r),
                border: Border.all(
                  color: isSelected
                      ? const Color(0xFF30E6B8)
                      : const Color(0xFF3A3A3A),
                  width: 1,
                ),
              ),
              child: Center(
                child: Text(
                  category.caseName ?? '',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: isSelected ? Colors.black : Colors.white,
                    fontWeight:
                        isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  // 构建分类内容区域
  Widget _buildCategoryContent(BuildContext context,
      List<PhotoPortraitCategory> categories, int selectedIndex) {
    if (categories.isEmpty || selectedIndex >= categories.length) {
      return const Center(
        child: Text(
          '暂无数据',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    final selectedCategory = categories[selectedIndex];
    final details = selectedCategory.details ?? [];

    if (details.isEmpty) {
      return const Center(
        child: Text(
          '该分类暂无内容',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    return Container(
      padding: EdgeInsets.all(16.w),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 12.w,
          mainAxisSpacing: 12.h,
          childAspectRatio: 0.75,
        ),
        itemCount: details.length,
        itemBuilder: (context, index) {
          final detail = details[index];
          return _buildImageCard(context, detail);
        },
      ),
    );
  }

  // 构建图片卡片
  Widget _buildImageCard(
      BuildContext context, PhotoPortraitCategoryDetail detail) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        color: const Color(0xFF2A2A2A),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.r),
        child: Stack(
          children: [
            // 图片
            CachedNetworkImage(
              imageUrl: detail.caseImage ?? '',
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: const Color(0xFF3A3A3A),
                child: const Center(
                  child: CircularProgressIndicator(
                    color: Color(0xFF30E6B8),
                    strokeWidth: 2,
                  ),
                ),
              ),
              errorWidget: (context, url, error) => Container(
                color: const Color(0xFF3A3A3A),
                child: const Center(
                  child: Icon(
                    Icons.image_not_supported,
                    color: Colors.grey,
                    size: 40,
                  ),
                ),
              ),
            ),
            // 渐变遮罩
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              height: 60.h,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.7),
                    ],
                  ),
                ),
              ),
            ),
            // 标题
            Positioned(
              bottom: 8.h,
              left: 8.w,
              right: 8.w,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    detail.caseTitle ?? '',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (detail.casePrompt?.isNotEmpty == true) ...[
                    SizedBox(height: 2.h),
                    Text(
                      detail.casePrompt ?? '',
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: Colors.white70,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
