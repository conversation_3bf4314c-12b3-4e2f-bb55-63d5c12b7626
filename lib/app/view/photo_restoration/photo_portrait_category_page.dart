import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_portrait.dart';


class PhotoPortraitCategoryPage extends ConsumerWidget {
  const PhotoPortraitCategoryPage({
    super.key,
    this.categoryId = 0,
  });

  /// 分类ID
  final int categoryId;

  // 模拟数据 - 写真分类数据（与主页面保持一致）
  List<PhotoPortraitCategory> get mockCategoryData => [
        PhotoPortraitCategory()
          ..caseName = "热门"
          ..id = 1
          ..sort = 1
          ..state = 1
          ..details = [
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/300?random=1"
              ..caseTitle = "热门写真1"
              ..casePrompt = "时尚热门写真风格"
              ..caseId = 101
              ..caseWidth = 200
              ..caseHeight = 300,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/350?random=2"
              ..caseTitle = "热门写真2"
              ..casePrompt = "流行写真风格"
              ..caseId = 102
              ..caseWidth = 200
              ..caseHeight = 350,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/280?random=3"
              ..caseTitle = "热门写真3"
              ..casePrompt = "经典写真风格"
              ..caseId = 103
              ..caseWidth = 200
              ..caseHeight = 280,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/320?random=4"
              ..caseTitle = "热门写真4"
              ..casePrompt = "现代写真风格"
              ..caseId = 104
              ..caseWidth = 200
              ..caseHeight = 320,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/290?random=17"
              ..caseTitle = "热门写真5"
              ..casePrompt = "优雅写真风格"
              ..caseId = 105
              ..caseWidth = 200
              ..caseHeight = 290,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/310?random=18"
              ..caseTitle = "热门写真6"
              ..casePrompt = "清新写真风格"
              ..caseId = 106
              ..caseWidth = 200
              ..caseHeight = 310,
          ],
        PhotoPortraitCategory()
          ..caseName = "双重曝光"
          ..id = 2
          ..sort = 2
          ..state = 1
          ..details = [
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/290?random=5"
              ..caseTitle = "双重曝光1"
              ..casePrompt = "艺术双重曝光效果"
              ..caseId = 201
              ..caseWidth = 200
              ..caseHeight = 290,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/310?random=6"
              ..caseTitle = "双重曝光2"
              ..casePrompt = "创意双重曝光"
              ..caseId = 202
              ..caseWidth = 200
              ..caseHeight = 310,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/270?random=7"
              ..caseTitle = "双重曝光3"
              ..casePrompt = "梦幻双重曝光"
              ..caseId = 203
              ..caseWidth = 200
              ..caseHeight = 270,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/340?random=8"
              ..caseTitle = "双重曝光4"
              ..casePrompt = "专业双重曝光"
              ..caseId = 204
              ..caseWidth = 200
              ..caseHeight = 340,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/300?random=19"
              ..caseTitle = "双重曝光5"
              ..casePrompt = "唯美双重曝光"
              ..caseId = 205
              ..caseWidth = 200
              ..caseHeight = 300,
          ],
        PhotoPortraitCategory()
          ..caseName = "网感大片"
          ..id = 3
          ..sort = 3
          ..state = 1
          ..details = [
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/300?random=9"
              ..caseTitle = "网感大片1"
              ..casePrompt = "时尚网感风格"
              ..caseId = 301
              ..caseWidth = 200
              ..caseHeight = 300,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/280?random=10"
              ..caseTitle = "网感大片2"
              ..casePrompt = "潮流网感风格"
              ..caseId = 302
              ..caseWidth = 200
              ..caseHeight = 280,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/320?random=11"
              ..caseTitle = "网感大片3"
              ..casePrompt = "个性网感风格"
              ..caseId = 303
              ..caseWidth = 200
              ..caseHeight = 320,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/290?random=12"
              ..caseTitle = "网感大片4"
              ..casePrompt = "酷炫网感风格"
              ..caseId = 304
              ..caseWidth = 200
              ..caseHeight = 290,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/310?random=20"
              ..caseTitle = "网感大片5"
              ..casePrompt = "前卫网感风格"
              ..caseId = 305
              ..caseWidth = 200
              ..caseHeight = 310,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/270?random=21"
              ..caseTitle = "网感大片6"
              ..casePrompt = "炫酷网感风格"
              ..caseId = 306
              ..caseWidth = 200
              ..caseHeight = 270,
          ],
        PhotoPortraitCategory()
          ..caseName = "宠物写真"
          ..id = 4
          ..sort = 4
          ..state = 1
          ..details = [
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/310?random=13"
              ..caseTitle = "宠物写真1"
              ..casePrompt = "可爱宠物风格"
              ..caseId = 401
              ..caseWidth = 200
              ..caseHeight = 310,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/270?random=14"
              ..caseTitle = "宠物写真2"
              ..casePrompt = "萌宠写真风格"
              ..caseId = 402
              ..caseWidth = 200
              ..caseHeight = 270,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/330?random=15"
              ..caseTitle = "宠物写真3"
              ..casePrompt = "温馨宠物风格"
              ..caseId = 403
              ..caseWidth = 200
              ..caseHeight = 330,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/300?random=16"
              ..caseTitle = "宠物写真4"
              ..casePrompt = "活泼宠物风格"
              ..caseId = 404
              ..caseWidth = 200
              ..caseHeight = 300,
          ],
      ];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container();
  }
}
