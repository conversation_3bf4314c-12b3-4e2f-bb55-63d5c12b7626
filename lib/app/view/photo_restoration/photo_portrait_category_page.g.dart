// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'photo_portrait_category_page.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$selectedCategoryIndexHash() =>
    r'f56edbb77d2cac83a3c193b3248486512ab546b4';

/// See also [SelectedCategoryIndex].
@ProviderFor(SelectedCategoryIndex)
final selectedCategoryIndexProvider =
    AutoDisposeNotifierProvider<SelectedCategoryIndex, int>.internal(
  SelectedCategoryIndex.new,
  name: r'selectedCategoryIndexProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$selectedCategoryIndexHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectedCategoryIndex = AutoDisposeNotifier<int>;
String _$photoPortraitCategoryDataHash() =>
    r'4138f6a11ef29b8180f046e6e3ac638c96b74959';

/// See also [PhotoPortraitCategoryData].
@ProviderFor(PhotoPortraitCategoryData)
final photoPortraitCategoryDataProvider = AutoDisposeNotifierProvider<
    PhotoPortraitCategoryData, List<PhotoPortraitCategory>>.internal(
  PhotoPortraitCategoryData.new,
  name: r'photoPortraitCategoryDataProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$photoPortraitCategoryDataHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PhotoPortraitCategoryData
    = AutoDisposeNotifier<List<PhotoPortraitCategory>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
